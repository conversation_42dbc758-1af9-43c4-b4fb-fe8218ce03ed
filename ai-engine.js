/**
 * AI Engine for Career Coach Application
 * Handles all AI-powered features including skill gap analysis,
 * learning path generation, role matching, and productivity optimization
 */

class AIEngine {
  constructor() {
    this.skillWeights = {
      'MUST_HAVE': 1.0,
      'PREFERRED': 0.7,
      'NICE_TO_HAVE': 0.3
    };
    
    this.spacedRepetitionIntervals = [1, 3, 7, 14, 30, 90]; // days
  }

  /**
   * Generate personalized learning plan based on skill gaps and career goals
   */
  async generateLearningPlan({ employee, currentSkills, targetRole, roleSkills, timeframe }) {
    try {
      // Analyze skill gaps
      const skillGaps = this.calculateSkillGaps(currentSkills, roleSkills);
      
      // Prioritize skills based on importance and gap size
      const prioritizedSkills = this.prioritizeSkills(skillGaps);
      
      // Generate learning activities
      const activities = this.generateLearningActivities(prioritizedSkills, timeframe);
      
      // Calculate target completion date
      const targetDate = this.calculateTargetDate(timeframe);
      
      return {
        name: `${targetRole} Career Development Plan`,
        description: `AI-generated learning plan to prepare ${employee.firstName} for ${targetRole} role`,
        targetSkills: prioritizedSkills.map(s => s.skillName),
        targetDate,
        activities,
        estimatedDuration: this.calculateTotalDuration(activities)
      };
      
    } catch (error) {
      throw new Error(`Learning plan generation failed: ${error.message}`);
    }
  }

  /**
   * Analyze skill gaps between current and target levels
   */
  async analyzeSkillGaps(employeeSkills) {
    const gaps = [];
    
    for (const skill of employeeSkills) {
      if (skill.currentLevel < skill.targetLevel) {
        const gap = skill.targetLevel - skill.currentLevel;
        const priority = this.calculateGapPriority(gap, skill.targetLevel);
        
        gaps.push({
          skill: skill.skill_ID,
          currentLevel: skill.currentLevel,
          targetLevel: skill.targetLevel,
          priority,
          recommendedActions: this.generateSkillRecommendations(skill, gap)
        });
      }
    }
    
    return gaps.sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority));
  }

  /**
   * Find matching internal roles based on employee skills and preferences
   */
  async findRoleMatches(employee, employeeSkills, availableRoles) {
    const matches = [];
    
    for (const role of availableRoles) {
      const matchScore = this.calculateRoleMatchScore(employeeSkills, role);
      const skillGapScore = this.calculateSkillGapScore(employeeSkills, role);
      const growthPotential = this.calculateGrowthPotential(employee, role);
      
      if (matchScore >= 60) { // Only suggest roles with 60%+ match
        matches.push({
          roleId: role.ID,
          roleTitle: role.roleTitle,
          matchScore,
          skillGapScore,
          growthPotential,
          reasoning: this.generateMatchReasoning(matchScore, skillGapScore, growthPotential),
          requiredSkills: this.extractRequiredSkills(role),
          missingSkills: this.identifyMissingSkills(employeeSkills, role)
        });
      }
    }
    
    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * Analyze productivity patterns and identify optimization opportunities
   */
  async analyzeProductivity(workflowData) {
    if (!workflowData || workflowData.length === 0) {
      return {
        averageProductivityScore: 0,
        peakHours: 'No data available',
        bottlenecks: [],
        recommendations: [],
        trends: []
      };
    }
    
    const avgScore = workflowData.reduce((sum, day) => sum + day.productivityScore, 0) / workflowData.length;
    const peakHours = this.identifyPeakHours(workflowData);
    const bottlenecks = this.identifyBottlenecks(workflowData);
    const recommendations = this.generateProductivityRecommendations(workflowData);
    const trends = this.calculateProductivityTrends(workflowData);
    
    return {
      averageProductivityScore: Math.round(avgScore * 100) / 100,
      peakHours,
      bottlenecks,
      recommendations,
      trends
    };
  }

  /**
   * Generate productivity improvement recommendations
   */
  async generateProductivityRecommendations(workflowData) {
    const recommendations = [];
    
    // Analyze patterns
    const avgFocusTime = workflowData.reduce((sum, day) => sum + day.focusTime, 0) / workflowData.length;
    const avgMeetingTime = workflowData.reduce((sum, day) => sum + day.meetingTime, 0) / workflowData.length;
    const avgProductivity = workflowData.reduce((sum, day) => sum + day.productivityScore, 0) / workflowData.length;
    
    // Generate recommendations based on patterns
    if (avgFocusTime < 2) {
      recommendations.push({
        category: 'Focus Time',
        recommendation: 'Schedule 2-3 hour blocks for deep work without interruptions',
        priority: 'HIGH',
        estimatedImpact: 'Increase productivity by 25-30%'
      });
    }
    
    if (avgMeetingTime > 4) {
      recommendations.push({
        category: 'Meeting Optimization',
        recommendation: 'Reduce meeting time by declining non-essential meetings and using async communication',
        priority: 'MEDIUM',
        estimatedImpact: 'Free up 1-2 hours daily for focused work'
      });
    }
    
    if (avgProductivity < 70) {
      recommendations.push({
        category: 'Task Management',
        recommendation: 'Use time-blocking and prioritize high-impact tasks during peak hours',
        priority: 'HIGH',
        estimatedImpact: 'Improve overall productivity by 20%'
      });
    }
    
    return recommendations;
  }

  /**
   * Generate spaced repetition schedule for learning activities
   */
  generateSpacedRepetitionSchedule(activities) {
    const reminders = [];
    const now = new Date();
    
    for (const activity of activities) {
      if (activity.status === 'COMPLETED') {
        // Generate review reminders using spaced repetition
        this.spacedRepetitionIntervals.forEach((interval, index) => {
          const reminderDate = new Date(now);
          reminderDate.setDate(reminderDate.getDate() + interval);
          
          reminders.push({
            type: index === 0 ? 'REVIEW' : 'PRACTICE',
            scheduledDate: reminderDate,
            message: `Time to review: ${activity.activityName}`,
            level: index + 1
          });
        });
      }
    }
    
    return reminders;
  }

  // Helper methods
  calculateSkillGaps(currentSkills, roleSkills) {
    const gaps = [];
    const currentSkillMap = new Map(currentSkills.map(s => [s.skill_ID, s.currentLevel]));
    
    for (const roleSkill of roleSkills) {
      const currentLevel = currentSkillMap.get(roleSkill.skill_ID) || 0;
      if (currentLevel < roleSkill.requiredLevel) {
        gaps.push({
          skillId: roleSkill.skill_ID,
          skillName: roleSkill.skill_ID, // Would be resolved from Skills entity
          currentLevel,
          requiredLevel: roleSkill.requiredLevel,
          gap: roleSkill.requiredLevel - currentLevel,
          importance: roleSkill.importance
        });
      }
    }
    
    return gaps;
  }

  prioritizeSkills(skillGaps) {
    return skillGaps.sort((a, b) => {
      const aWeight = this.skillWeights[a.importance] * a.gap;
      const bWeight = this.skillWeights[b.importance] * b.gap;
      return bWeight - aWeight;
    });
  }

  generateLearningActivities(prioritizedSkills, timeframe) {
    const activities = [];
    const timeframeWeeks = this.parseTimeframe(timeframe);
    
    prioritizedSkills.forEach((skill, index) => {
      const dueDate = new Date();
      dueDate.setDate(dueDate.getDate() + (index + 1) * Math.floor((timeframeWeeks * 7) / prioritizedSkills.length));
      
      activities.push({
        name: `Master ${skill.skillName}`,
        type: 'COURSE',
        description: `Complete training to reach level ${skill.requiredLevel} in ${skill.skillName}`,
        estimatedHours: skill.gap * 10, // 10 hours per skill level
        dueDate,
        resourceUrl: `https://learning.company.com/skills/${skill.skillId}`,
        priority: skill.importance === 'MUST_HAVE' ? 5 : skill.importance === 'PREFERRED' ? 3 : 1
      });
    });
    
    return activities;
  }

  calculateRoleMatchScore(employeeSkills, role) {
    // Simplified matching algorithm
    // In a real implementation, this would be more sophisticated
    const skillMap = new Map(employeeSkills.map(s => [s.skill_ID, s.currentLevel]));
    let totalWeight = 0;
    let matchedWeight = 0;
    
    // This would need to be joined with RoleSkills in real implementation
    const roleRequiredSkills = []; // Placeholder
    
    for (const reqSkill of roleRequiredSkills) {
      const weight = this.skillWeights[reqSkill.importance] || 0.5;
      totalWeight += weight;
      
      const employeeLevel = skillMap.get(reqSkill.skill_ID) || 0;
      if (employeeLevel >= reqSkill.requiredLevel) {
        matchedWeight += weight;
      } else {
        // Partial credit for partial skill match
        matchedWeight += weight * (employeeLevel / reqSkill.requiredLevel);
      }
    }
    
    return totalWeight > 0 ? Math.round((matchedWeight / totalWeight) * 100) : 0;
  }

  calculateSkillGapScore(employeeSkills, role) {
    // Calculate how much skill development is needed
    return Math.random() * 30 + 10; // Placeholder implementation
  }

  calculateGrowthPotential(employee, role) {
    // Calculate growth potential based on career trajectory
    return Math.random() * 30 + 70; // Placeholder implementation
  }

  generateMatchReasoning(matchScore, skillGapScore, growthPotential) {
    return `Strong match (${matchScore}%) with manageable skill gaps (${skillGapScore}% development needed). High growth potential (${growthPotential}%) based on career trajectory.`;
  }

  extractRequiredSkills(role) {
    return ['JavaScript', 'React', 'Node.js']; // Placeholder
  }

  identifyMissingSkills(employeeSkills, role) {
    return ['Advanced React', 'GraphQL']; // Placeholder
  }

  identifyPeakHours(workflowData) {
    // Analyze when productivity is highest
    return '9:00 AM - 11:00 AM'; // Placeholder
  }

  identifyBottlenecks(workflowData) {
    return ['Too many meetings', 'Email interruptions']; // Placeholder
  }

  calculateProductivityTrends(workflowData) {
    return workflowData.map(day => ({
      date: day.date,
      score: day.productivityScore,
      focusTime: day.focusTime
    }));
  }

  calculateGapPriority(gap, targetLevel) {
    if (gap >= 3) return 'HIGH';
    if (gap >= 2) return 'MEDIUM';
    return 'LOW';
  }

  generateSkillRecommendations(skill, gap) {
    const recommendations = [];
    if (gap >= 2) {
      recommendations.push('Enroll in comprehensive training course');
      recommendations.push('Find a mentor in this skill area');
    }
    if (gap >= 1) {
      recommendations.push('Practice with hands-on projects');
      recommendations.push('Join relevant communities or forums');
    }
    return recommendations;
  }

  getPriorityWeight(priority) {
    const weights = { 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
    return weights[priority] || 1;
  }

  parseTimeframe(timeframe) {
    // Parse timeframe like "3 months", "6 weeks", etc.
    const match = timeframe.match(/(\d+)\s*(week|month|year)s?/i);
    if (!match) return 12; // Default to 12 weeks
    
    const [, number, unit] = match;
    const multipliers = { week: 1, month: 4, year: 52 };
    return parseInt(number) * (multipliers[unit.toLowerCase()] || 1);
  }

  calculateTargetDate(timeframe) {
    const weeks = this.parseTimeframe(timeframe);
    const targetDate = new Date();
    targetDate.setDate(targetDate.getDate() + weeks * 7);
    return targetDate;
  }

  calculateTotalDuration(activities) {
    return activities.reduce((total, activity) => total + activity.estimatedHours, 0);
  }
}

module.exports = { AIEngine };
